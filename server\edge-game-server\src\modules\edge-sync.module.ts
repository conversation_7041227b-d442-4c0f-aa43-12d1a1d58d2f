import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';

// 服务
import { EdgeSyncService } from '../services/edge-sync.service';
import { EdgeDataSyncService } from '../services/edge-data-sync.service';
import { EdgeStateSyncService } from '../services/edge-state-sync.service';

// 控制器
import { EdgeSyncController } from '../controllers/edge-sync.controller';

/**
 * 边缘同步模块
 * 处理边缘节点与中心节点之间的数据同步
 */
@Module({
  imports: [
    ConfigModule,
    EventEmitterModule,
    ScheduleModule,
  ],
  
  controllers: [
    EdgeSyncController,
  ],
  
  providers: [
    EdgeSyncService,
    EdgeDataSyncService,
    EdgeStateSyncService,
  ],
  
  exports: [
    EdgeSyncService,
    EdgeDataSyncService,
    EdgeStateSyncService,
  ],
})
export class EdgeSyncModule {}
